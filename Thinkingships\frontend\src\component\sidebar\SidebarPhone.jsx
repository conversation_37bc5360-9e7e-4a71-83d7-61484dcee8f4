import { Home } from 'lucide-react'
import { useLocation, useNavigate } from 'react-router-dom'
const ranking = (color, h, w, text) => {
    return (
        <div className="flex flex-col gap-3 rounded-md cursor-pointer transition-all justify-center items-center py-2">
            <svg xmlns="http://www.w3.org/2000/svg" width={w} height={h} viewBox="0 0 30 30" fill="none">
                <path d="M13.5 12H16.5C18.621 12 19.6815 12 20.34 12.66C21 13.3185 21 14.379 21 16.5V25.5C21 23.379 21 22.3185 21.66 21.66C22.317 21 23.3775 21 25.5 21C27.6225 21 28.6815 21 29.34 21.66C30 22.3185 30 23.379 30 25.5V30H0C0 27.879 8.9407e-08 26.8185 0.66 26.16C1.317 25.5 2.3775 25.5 4.5 25.5C6.6225 25.5 7.6815 25.5 8.34 26.16C9 26.817 9 27.8775 9 30V16.5C9 14.379 9 13.3185 9.66 12.66C10.317 12 11.3775 12 13.5 12ZM13.719 1.5345C14.289 0.51 14.574 0 15 0C15.426 0 15.711 0.51 16.281 1.5345L16.428 1.7985C16.59 2.0895 16.671 2.2335 16.797 2.3295C16.9245 2.4255 17.082 2.4615 17.397 2.532L17.682 2.598C18.789 2.8485 19.3425 2.973 19.4745 3.396C19.6065 3.819 19.2285 4.2615 18.474 5.1435L18.279 5.3715C18.0645 5.622 17.9565 5.7465 17.9085 5.9025C17.8605 6.0585 17.877 6.225 17.9085 6.5595L17.9385 6.864C18.0525 8.0415 18.1095 8.631 17.766 8.892C17.421 9.1545 16.902 8.9145 15.8655 8.4375L15.5985 8.3145C15.303 8.1795 15.156 8.1105 15 8.1105C14.844 8.1105 14.697 8.1795 14.4015 8.3145L14.1345 8.4375C13.098 8.9145 12.579 9.1545 12.234 8.892C11.889 8.631 11.9475 8.0415 12.0615 6.864L12.0915 6.5595C12.123 6.225 12.1395 6.0585 12.0915 5.9025C12.0435 5.7465 11.9355 5.622 11.721 5.3715L11.526 5.1435C10.7715 4.2615 10.3935 3.8205 10.5255 3.396C10.6575 2.973 11.211 2.8485 12.318 2.598L12.603 2.532C12.918 2.4615 13.0755 2.427 13.203 2.3295C13.329 2.2335 13.41 2.0895 13.572 1.7985L13.719 1.5345Z" fill={color} />
            </svg>
            <span className="text-sm font-medium text-gray-800">{text}</span>
        </div>
    )
}
const Skrivee = (color, h, w, text) => {
    return (
        <div className="flex flex-col gap-3 rounded-md cursor-pointer transition-all justify-center items-center py-2">
            <svg xmlns="http://www.w3.org/2000/svg" width={w} height={h} viewBox="0 0 30 30" fill="none">
                <path d="M28.4278 0.740671L29.2506 1.55458C30.3843 2.6775 30.2128 4.66813 28.8643 6.00073L11.8969 22.7852L6.39956 24.7744C5.70926 25.0255 5.03709 24.6999 4.90043 24.0502C4.85439 23.8145 4.87613 23.5707 4.96318 23.3466L7.01316 17.8617L23.9332 1.12279C25.2817 -0.209809 27.294 -0.382247 28.4278 0.740671ZM11.1564 2.40987C11.3395 2.40987 11.5208 2.44556 11.69 2.51488C11.8592 2.58421 12.0129 2.68582 12.1424 2.81392C12.2719 2.94202 12.3747 3.0941 12.4447 3.26147C12.5148 3.42883 12.5509 3.60822 12.5509 3.78938C12.5509 3.97054 12.5148 4.14992 12.4447 4.31729C12.3747 4.48466 12.2719 4.63674 12.1424 4.76484C12.0129 4.89294 11.8592 4.99455 11.69 5.06388C11.5208 5.1332 11.3395 5.16889 11.1564 5.16889H5.57817C4.83846 5.16889 4.12905 5.45957 3.60599 5.97698C3.08294 6.4944 2.78909 7.19616 2.78909 7.9279V24.482C2.78909 25.2137 3.08294 25.9155 3.60599 26.4329C4.12905 26.9503 4.83846 27.241 5.57817 27.241H22.3127C23.0524 27.241 23.7618 26.9503 24.2849 26.4329C24.8079 25.9155 25.1018 25.2137 25.1018 24.482V18.9639C25.1018 18.5981 25.2487 18.2472 25.5102 17.9885C25.7718 17.7298 26.1265 17.5844 26.4963 17.5844C26.8662 17.5844 27.2209 17.7298 27.4824 17.9885C27.744 18.2472 27.8909 18.5981 27.8909 18.9639V24.482C27.8909 25.9454 27.3032 27.349 26.2571 28.3838C25.211 29.4186 23.7921 30 22.3127 30H5.57817C4.09875 30 2.67992 29.4186 1.63381 28.3838C0.587699 27.349 0 25.9454 0 24.482V7.9279C0 6.46443 0.587699 5.0609 1.63381 4.02607C2.67992 2.99124 4.09875 2.40987 5.57817 2.40987H11.1564Z" fill={color} />
            </svg>
            <span className="text-sm font-medium text-gray-800">{text}</span>
        </div>
    )
}
const pitch = (color, h, w, text) => {

    return (
        <div className="flex flex-col gap-3 rounded-md cursor-pointer transition-all justify-center items-center py-2">
            <svg xmlns="http://www.w3.org/2000/svg" width={w} height={h} viewBox="0 0 30 30" fill="none">
                <path d="M28.3267 1.67281C27.7959 1.14195 27.1654 0.720937 26.4714 0.433888C25.7774 0.146839 25.0336 -0.000604924 24.2825 1.86527e-06C23.5314 0.000608654 22.7878 0.149254 22.0943 0.437425C21.4007 0.725595 20.771 1.14763 20.241 1.67935L18.541 3.38425L26.618 11.4558L28.3311 9.74006C29.4009 8.66936 30.0012 7.21796 30 5.70508C29.9988 4.19221 29.3983 2.74178 28.3267 1.67281ZM3.96486 17.9858L16.9979 4.92584L25.0772 12.9974L12.0506 26.0509C11.7076 26.395 11.2922 26.6583 10.8345 26.8217L2.18425 29.9049C1.89254 30.009 1.57725 30.0281 1.27509 29.96C0.972932 29.892 0.696347 29.7395 0.477554 29.5205C0.258762 29.3014 0.106767 29.0248 0.0392759 28.7228C-0.0282156 28.4208 -0.008426 28.1058 0.0963398 27.8146L3.20423 19.1856C3.36662 18.7348 3.6263 18.3252 3.96486 17.9858ZM1.12722 1.67717H17.1614L14.9885 3.85456H1.12722C0.838205 3.85456 0.56103 3.73986 0.356667 3.53569C0.152305 3.33152 0.0374949 3.05461 0.0374949 2.76587C0.0374949 2.47713 0.152305 2.20021 0.356667 1.99604C0.56103 1.79187 0.838205 1.67717 1.12722 1.67717ZM1.12722 6.03196H12.8156L10.6427 8.20935H1.12722C0.838205 8.20935 0.56103 8.09465 0.356667 7.89048C0.152305 7.68631 0.0374949 7.40939 0.0374949 7.12065C0.0374949 6.83191 0.152305 6.555 0.356667 6.35083C0.56103 6.14666 0.838205 6.03196 1.12722 6.03196ZM1.12722 10.3867H8.47195L6.29686 12.5641H1.12722C0.838205 12.5641 0.56103 12.4494 0.356667 12.2453C0.152305 12.0411 0.0374949 11.7642 0.0374949 11.4754C0.0374949 11.1867 0.152305 10.9098 0.356667 10.7056C0.56103 10.5014 0.838205 10.3867 1.12722 10.3867Z"
                    fill={color} />
            </svg>
            <span className="text-sm font-medium text-gray-800">{text}</span>
        </div>
    )
}
function SidebarPhone() {
    const location = useLocation();
    const navigate = useNavigate();

    const color = "#4A99F8";
    const height = 22;
    const width = 24;
    const isActive = (path) => location.pathname === path;

    const SidebarItem = ({ path, render }) => (
        <div
            onClick={() => navigate(path)}
            className={`cursor-pointer transition-all  flex items-center
        ${isActive(path) ? 'border-t-4 border-[#4A99F8]' : 'hover:border-t-4 hover:border-[#80c3d4]'}
      `}
        >
            {render()}
        </div>
    );
  return (
    <div className="fixed bottom-0 w-full h-20 shadow-[0_-4px_10px_rgba(0,0,0,0.1)] 
 bg-white flex items-center justify-between 
            rounded-t-2xl rounded-b-none overflow-hidden z-50 px-6">
        <SidebarItem path="/home" render={() => ( 
             <div className="flex flex-col gap-3 rounded-md cursor-pointer transition-all justify-center
             items-center py-2">
            <Home className="w-8 h-6 text-[#4A99F8]" />
            <span className="text-sm font-medium text-gray-800">Home</span>
            </div>
            )}/>
        <SidebarItem path="/ranking" render={() => ranking(color, height, width, "Ranking")} />
        <SidebarItem path="/profile" render={() => (
                    <div className="avatar flex-1 flex justify-center items-center py-2">
                        <div className="ring-primary ring-offset-base-100 w-14 h-14 rounded-full ring-2 ring-offset-2 overflow-hidden">
                            <img
                                src="https://img.daisyui.com/images/profile/demo/<EMAIL>"
                                className="w-full h-full object-cover rounded-full"
                                alt="Profile"
                            />
                        </div>
                    </div>
                )} />
        <SidebarItem path="/skrivee" render={() => Skrivee(color, height, width, "Skrivee")} />
        <SidebarItem path="/pitch" render={() => pitch(color, height, width, "Pitch")} />
    </div>
  )
}

export default SidebarPhone