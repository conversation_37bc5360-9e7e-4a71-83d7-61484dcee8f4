import { useLocation, useNavigate } from 'react-router-dom';
import Dashboard from '../btns/Dashboard';
import Help from '../btns/Help';
import Logout from '../btns/Logout';
import Messages from '../btns/Messages';
import Notification from '../btns/Notification';
import Pitch from '../btns/Pitch';
import Ranking from '../btns/Ranking';
import Settings from '../btns/Settings';
import Skrivee from '../btns/Skrivee';
import Logo from '/logo/Logo.svg';

function Sidebar() {
    const location = useLocation();
    const navigate = useNavigate();

    const color = "#4A99F8";
    const height = 22;
    const width = 24;
    const isActive = (path) => location.pathname === path;

    const SidebarItem = ({ path, render }) => (
        <div
            onClick={() => navigate(path)}
            className={`cursor-pointer transition-all duration-300 rounded-l-md flex items-center hover:scale-105 hover:bg-blue-50/50 mx-1 sm:mx-2 md:mx-3 p-1 sm:p-2
        ${isActive(path) ? 'border-r-2 sm:border-r-3 md:border-r-4 border-[#4A99F8] bg-blue-50/30' : 'hover:border-r-2 sm:hover:border-r-3 md:hover:border-r-4 hover:border-[#80c3d4]'}
      `}
        >
            {render()}
        </div>
    );
return (
  <div className="w-auto h-screen flex flex-col">
    {/* TOP SECTION: Logo + Avatar */}
    <div className="px-2 sm:px-3 md:px-4">
      <img
        src={Logo}
        className="h-10 w-20 sm:h-12 sm:w-24 md:h-14 md:w-32 mb-3 sm:mb-4 cursor-pointer hover:scale-105 transition-transform duration-300"
        onClick={() => navigate('/home')}
      />
      <SidebarItem path="/profile" render={() => (
        <div className="avatar flex-1 flex justify-center items-center">
          <div className="ring-primary ring-offset-base-100 w-10 h-10 sm:w-12 sm:h-12 md:w-14 md:h-14 rounded-full ring-2 ring-offset-2 overflow-hidden hover:ring-[#4A99F8] transition-all duration-300">
            <img
              src="https://img.daisyui.com/images/profile/demo/<EMAIL>"
              className="w-full h-full object-cover rounded-full hover:scale-110 transition-transform duration-300"
              alt="Profile"
            />
          </div>
        </div>
      )} />
    </div>

    {/* MIDDLE SECTION: Nav Items - evenly spaced */}
    <div className="flex flex-col justify-evenly flex-grow px-1 sm:px-2 md:px-3">
      <SidebarItem path="/skrivee" render={() => <Skrivee color={color} h={height} w={width} txt="Skrivee" />} />
      <SidebarItem path="/ranking" render={() => <Ranking color={color} h={height} w={width} txt="Ranking" />} />
      <SidebarItem path="/pitch" render={() => <Pitch color={color} h={height} w={width} txt="Pitch" />} />
      <SidebarItem path="/messages" render={() => <Messages color={color} h={height} w={width} txt="Messages" />} />
      <SidebarItem path="/notifications" render={() => <Notification color={color} h={height} w={width} txt="Notification" />} />
      <SidebarItem path="/dashboard" render={() => <Dashboard color={color} h={height} w={width} txt="Dashboard" />} />
      <SidebarItem path="/help" render={() => <Help color={color} h={height} w={width} txt="Help" />} />
      <SidebarItem path="/settings" render={() => <Settings color={color} h={height} w={width} txt="Settings" />} />
      <Logout color={color} h={height} w={width} txt="Logout" />
    </div>

  </div>
)

}


export default Sidebar