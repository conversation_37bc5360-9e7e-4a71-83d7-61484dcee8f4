import Sidebar from './component/sidebar/Sidebar';
import { useSelector } from 'react-redux';
import { Navigate, Outlet, useLocation } from 'react-router-dom';
import Navbar from './component/navbar/Navbar';
import SidebarPhone from './component/sidebar/SidebarPhone';
import Navbar<PERSON>hn from './component/navbar/NavbarPhn';

const ProtectedRoute = () => {
    const { isAuthenticated, user } = useSelector((state) => state.user);
    const location = useLocation();

    // Authentication checking removed for easy development access
    // if (location.pathname !== "/" &&
    //     !isAuthenticated) return <Navigate to="/" replace />;
    // if (
    //     (location.pathname !== "/profile/edit") &&
    //     (!user?.isProfileComplete || !user?.isVerified)
    // ) {
    //     return <Navigate to="/profile/edit" replace />;
    // }

    return (
        <>
        {/* Desktop/Tablet Navbar */}
        <div className="hidden sm:flex">
            <Navbar/>
        </div>

        {/* Mobile Navbar */}
        <div className="sm:hidden flex">
            <NavbarPhn/>
        </div>

        <div className="flex min-h-screen">
            {/* Desktop/Tablet Sidebar */}
            <div className="hidden sm:block">
                <aside className="sticky top-0 h-screen bg-white px-2 sm:px-4 md:px-6 pt-2 sm:pt-3 md:pt-4 w-16 sm:w-20 md:w-24 lg:w-44 flex flex-col overflow-hidden border-r border-gray-200 shadow-sm">
                    <Sidebar />
                </aside>
            </div>

            {/* Mobile Bottom Sidebar */}
            <div className="sm:hidden">
                <SidebarPhone/>
            </div>

            {/* Main content area */}
            <main className="flex-1 mt-12 sm:mt-14 md:mt-16 lg:mt-20 py-3 sm:py-4 md:py-6 px-3 sm:px-4 md:px-6 lg:pr-0 min-h-screen mb-16 sm:mb-0 max-w-full overflow-x-hidden">
                <Outlet />
            </main>
        </div>
        </>
    );

};

export default ProtectedRoute;
