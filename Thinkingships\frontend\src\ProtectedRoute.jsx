import Sidebar from './component/sidebar/Sidebar';
import { useSelector } from 'react-redux';
import { Navigate, Outlet, useLocation } from 'react-router-dom';
import Navbar from './component/navbar/Navbar';
import SidebarPhone from './component/sidebar/SidebarPhone';
import Navbar<PERSON>hn from './component/navbar/NavbarPhn';

const ProtectedRoute = () => {
    const { isAuthenticated, user } = useSelector((state) => state.user);
    const location = useLocation();

    // Authentication checking removed for easy development access
    // if (location.pathname !== "/" &&
    //     !isAuthenticated) return <Navigate to="/" replace />;
    // if (
    //     (location.pathname !== "/profile/edit") &&
    //     (!user?.isProfileComplete || !user?.isVerified)
    // ) {
    //     return <Navigate to="/profile/edit" replace />;
    // }

    return (
        <>
        <div className="md:flex hidden">
        <Navbar/>
        </div>
        <div className="md:hidden flex">
        <NavbarPhn/>
        </div>
        <div className="flex min-h-screen">
            {/* Sidebar */}
            <div className="block">
                <aside
                className="
        sticky top-0 h-screen
        bg-gray-100 px-6 pt-2
        w-20 sm:w-24 md:w-44
        hidden xs:flex flex-col
        md:flex
        overflow-hidden
        md:border-r-1  md:border-blue-100
        md:z-100
        md:bg-[#FFFFFF]
        
        "
            >
            <Sidebar />
            </aside>
            <div className="flex md:hidden ">
                <SidebarPhone/>
            </div>
            </div>
            

            {/* Main content area */}
            <main className="flex-1 md:mt-10 mt-20 py-6 pl-6 pr-6 md:pr-0 min-h-screen md:mb-0 mb-20 max-w-screen overflow-x-hidden">
                    <Outlet />
            </main>
        </div>
        </>
    );

};

export default ProtectedRoute;
