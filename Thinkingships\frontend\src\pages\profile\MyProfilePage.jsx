import { BiGlobe } from 'react-icons/bi';
import { FaFacebook, FaInstagram, FaLinkedin, FaTwitter } from 'react-icons/fa';
import { SiGoodreads } from 'react-icons/si';

function MyProfilePage({ user }) {
  const profileData = {
    username: '<PERSON>',
    fullName: '<PERSON>',
    rank: 1800,
    fans: 250,
    faves: 550,
    bio: 'I am a passionate writer with a keen eye for detail, dedicated to exploring the complexities of the human experience through captivating storytelling that leaves a lasting impression.',
    passion: 'Lorem ipsum dolor sit amet consectetur. Enim rhoncus blandit consequat vel nulla at feugiat volutpat.',
    gender: 'Male',
    dob: '04-02-1991',
    mobile: '+012345678',
    occupation: 'Student',
    email: '<EMAIL>',
    location: 'New Delhi',
    language: 'English',
    social: {
      instagram: '<PERSON> Agarwal',
      facebook: '<PERSON>',
      linkedin: '<PERSON>',
      twitter: '<PERSON>',
      goodreads: '<PERSON>',
    }
  };

  return (
    <div className="flex-1 w-full bg-white">
      {/* Profile Header */}
      <div className="bg-white p-6 border-b border-gray-200 text-center">
        <div className="flex justify-center gap-16 items-center mb-4">
          {/* Fans */}
          <div>
            <div className="text-3xl font-semibold">{profileData.fans}</div>
            <div className="text-sm text-gray-600">FANS</div>
          </div>

          {/* Circular Profile Image */}
          <div className="w-28 h-28 rounded-full overflow-hidden border-4 border-white shadow-lg">
            <img
              src={user?.profileImage || "https://via.placeholder.com/112"}
              alt={profileData.fullName}
              className="w-full h-full object-cover"
            />
          </div>

          {/* Faves */}
          <div>
            <div className="text-3xl font-semibold">{profileData.faves}</div>
            <div className="text-sm text-gray-600">FAVES</div>
          </div>
        </div>

        {/* Username + Rank */}
        <div className="text-sm text-gray-500">Rank {profileData.rank}</div>
        <h2 className="text-2xl font-semibold">{profileData.username}</h2>

        {/* Edit Profile Button */}
        <div className="flex justify-center mt-4 mb-4">
          <button className="bg-[#2E3A59] hover:bg-[#1f2937] text-white px-6 py-2 rounded text-sm">
            Edit Profile
          </button>
        </div>

        {/* Bio */}
        <p className="text-gray-600 text-sm max-w-2xl mx-auto mb-6">{profileData.bio}</p>

        {/* Tabs */}
        <div className="flex justify-center gap-4 max-w-md mx-auto">
          <button className="bg-[#2E3A59] text-white px-8 py-2 rounded-md flex-1 text-sm font-medium">INFO</button>
          <button className="border border-gray-300 px-8 py-2 rounded-md flex-1 text-sm font-medium hover:bg-gray-100">SKRIVEE</button>
          <button className="border border-gray-300 px-8 py-2 rounded-md flex-1 text-sm font-medium hover:bg-gray-100">FEEDBACK</button>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex flex-col md:flex-row gap-6 p-6">
        {/* Info Card */}
        <div className="bg-white rounded border border-gray-300 p-6 flex-1">
          <div className="flex items-center justify-between mb-6">
            <h3 className="font-semibold text-base text-[#7D1F26]">{profileData.fullName}</h3>
            <div className="w-8 h-8 bg-[#2E3A59] rounded-full flex items-center justify-center text-white">
              <BiGlobe size={18} />
            </div>
          </div>

          <div className="space-y-4 text-sm text-gray-700">
            <div>
              <strong className="block text-[#2E3A59]">Passion</strong>
              <p>{profileData.passion}</p>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <strong className="block text-[#2E3A59]">Gender</strong>
                <p>{profileData.gender}</p>
              </div>
              <div>
                <strong className="block text-[#2E3A59]">Date of Birth</strong>
                <p>{profileData.dob}</p>
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <strong className="block text-[#2E3A59]">Mobile Number</strong>
                <p>{profileData.mobile}</p>
              </div>
              <div>
                <strong className="block text-[#2E3A59]">Occupation</strong>
                <p>{profileData.occupation}</p>
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <strong className="block text-[#2E3A59]">Email Id</strong>
                <p>{profileData.email}</p>
              </div>
              <div>
                <strong className="block text-[#2E3A59]">Location</strong>
                <p>{profileData.location}</p>
              </div>
            </div>
            <div>
              <strong className="block text-[#2E3A59]">Language</strong>
              <p>{profileData.language}</p>
            </div>
          </div>
        </div>

        {/* Ads */}
        <div className="w-full md:w-80 space-y-6">
          {[1, 2].map((_, idx) => (
            <div key={idx} className="bg-white rounded shadow border border-blue-100 p-4">
              <div className="text-center text-blue-600 font-medium mb-2">
                Take your website to the next level
              </div>
              <div className="bg-blue-50 p-4 rounded flex justify-center">
                <img src="https://via.placeholder.com/240x120" alt="Ad" className="rounded" />
              </div>
              <button className="mt-2 bg-blue-600 text-white text-xs px-3 py-1 rounded-full mx-auto block">
                Get started
              </button>
            </div>
          ))}
        </div>
      </div>

      {/* Social Links */}
      <div className="bg-white rounded border border-gray-300 p-6 mt-6 mx-6">
        <div className="space-y-4">
          {[
            { icon: <FaInstagram />, color: 'from-purple-500 to-pink-500', label: profileData.social.instagram },
            { icon: <FaFacebook />, color: 'bg-blue-600', label: profileData.social.facebook },
            { icon: <FaLinkedin />, color: 'bg-blue-700', label: profileData.social.linkedin },
            { icon: <FaTwitter />, color: 'bg-blue-400', label: profileData.social.twitter },
            { icon: <SiGoodreads />, color: 'bg-amber-700', label: profileData.social.goodreads },
          ].map(({ icon, color, label }, idx) => (
            <div key={idx} className="flex items-center gap-3">
              <div className={`w-10 h-10 ${color.includes('from') ? `bg-gradient-to-br ${color}` : color} rounded-full flex items-center justify-center text-white`}>
                {icon}
              </div>
              <span className="text-gray-700 text-sm">{label}</span>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}

export default MyProfilePage;
