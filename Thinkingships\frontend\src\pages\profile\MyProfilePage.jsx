import { BiGlobe } from 'react-icons/bi';
import { FaFacebook, FaInstagram, FaLinkedin, FaTwitter } from 'react-icons/fa';
import { SiGoodreads } from 'react-icons/si';

function MyProfilePage({ user }) {
  const profileData = {
    username: '<PERSON>',
    fullName: '<PERSON>',
    rank: 1800,
    fans: 250,
    faves: 550,
    bio: 'I am a passionate writer with a keen eye for detail, dedicated to exploring the complexities of the human experience through captivating storytelling that leaves a lasting impression.',
    passion: 'Lorem ipsum dolor sit amet consectetur. Enim rhoncus blandit consequat vel nulla at feugiat volutpat.',
    gender: 'Male',
    dob: '04-02-1991',
    mobile: '+012345678',
    occupation: 'Student',
    email: '<EMAIL>',
    location: 'New Delhi',
    language: 'English',
    social: {
      instagram: 'Ravi Agarwal',
      facebook: '<PERSON>',
      linkedin: '<PERSON>',
      twitter: '<PERSON>',
      goodreads: '<PERSON>',
    }
  };

  return (
    <div className="flex-1 w-full bg-gradient-to-br from-gray-50 via-white to-blue-50 min-h-screen">
      {/* Profile Header */}
      <div className="bg-white/80 backdrop-blur-sm p-8 border-b border-gray-200 text-center shadow-lg relative overflow-hidden">
        {/* Background Pattern */}
        <div className="absolute inset-0 bg-gradient-to-r from-blue-50/30 to-purple-50/30"></div>
        <div className="relative z-10">
          <div className="flex justify-center gap-16 items-center mb-6">
            {/* Fans */}
            <div className="group cursor-pointer">
              <div className="text-4xl font-bold text-gray-800 group-hover:text-[#4A99F8] transition-all duration-300 group-hover:scale-110">
                {profileData.fans}
              </div>
              <div className="text-sm text-gray-600 font-semibold tracking-wider">FANS</div>
              <div className="w-12 h-0.5 bg-gradient-to-r from-[#4A99F8] to-purple-500 mx-auto mt-1 transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300"></div>
            </div>

            {/* Circular Profile Image */}
            <div className="relative group">
              <div className="w-32 h-32 rounded-full overflow-hidden border-4 border-white shadow-2xl group-hover:shadow-3xl transition-all duration-500 group-hover:scale-105">
                <img
                  src={user?.profileImage || "https://img.daisyui.com/images/profile/demo/<EMAIL>"}
                  alt={profileData.fullName}
                  className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500"
                />
              </div>
              <div className="absolute inset-0 rounded-full bg-gradient-to-tr from-[#4A99F8]/20 to-purple-500/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
            </div>

            {/* Faves */}
            <div className="group cursor-pointer">
              <div className="text-4xl font-bold text-gray-800 group-hover:text-[#4A99F8] transition-all duration-300 group-hover:scale-110">
                {profileData.faves}
              </div>
              <div className="text-sm text-gray-600 font-semibold tracking-wider">FAVES</div>
              <div className="w-12 h-0.5 bg-gradient-to-r from-purple-500 to-[#4A99F8] mx-auto mt-1 transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300"></div>
            </div>
          </div>

          {/* Username + Rank */}
          <div className="text-sm text-gray-500 mb-2 font-medium">Rank {profileData.rank}</div>
          <h2 className="text-3xl font-bold mb-6 text-gray-800 hover:text-[#4A99F8] transition-colors duration-300">{profileData.username}</h2>

          {/* Edit Profile Button */}
          <div className="flex justify-center mt-6 mb-8">
            <button className="group flex items-center gap-3 bg-gradient-to-r from-[#2E3A59] to-[#4A99F8] hover:from-[#4A99F8] hover:to-blue-600 text-white px-8 py-3 rounded-full text-sm font-semibold shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300">
              <svg className="w-4 h-4 group-hover:rotate-12 transition-transform duration-300" fill="currentColor" viewBox="0 0 20 20">
                <path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z" />
              </svg>
              <span>Edit Profile</span>
            </button>
          </div>

          {/* Bio */}
          <p className="text-gray-700 text-sm max-w-3xl mx-auto mb-8 leading-relaxed bg-white/50 backdrop-blur-sm p-4 rounded-lg shadow-sm border border-gray-100">
            {profileData.bio}
          </p>

          {/* Tabs */}
          <div className="flex justify-center gap-2 max-w-lg mx-auto">
            <button className="bg-gradient-to-r from-[#2E3A59] to-[#4A99F8] text-white px-8 py-3 rounded-lg flex-1 text-sm font-semibold shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300">
              INFO
            </button>
            <button className="border-2 border-gray-300 px-8 py-3 rounded-lg flex-1 text-sm font-semibold hover:bg-gradient-to-r hover:from-blue-50 hover:to-purple-50 hover:border-[#4A99F8] hover:text-[#4A99F8] transition-all duration-300 transform hover:scale-105 shadow-md hover:shadow-lg">
              SKRIVEE
            </button>
            <button className="border-2 border-gray-300 px-8 py-3 rounded-lg flex-1 text-sm font-semibold hover:bg-gradient-to-r hover:from-blue-50 hover:to-purple-50 hover:border-[#4A99F8] hover:text-[#4A99F8] transition-all duration-300 transform hover:scale-105 shadow-md hover:shadow-lg">
              FEEDBACK
            </button>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex flex-col lg:flex-row gap-6 p-8">
        {/* Info Card */}
        <div className="bg-white/90 backdrop-blur-sm rounded-2xl border border-gray-200 p-8 flex-1 max-w-2xl shadow-2xl hover:shadow-3xl transition-all duration-500 relative overflow-hidden group">
          {/* Background Pattern */}
          <div className="absolute inset-0 bg-gradient-to-br from-blue-50/30 via-transparent to-purple-50/30 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

          <div className="relative z-10">
            <div className="flex items-center justify-between mb-8">
              <h3 className="font-bold text-2xl text-gray-800 hover:text-[#4A99F8] transition-colors duration-300">{profileData.fullName}</h3>
              <div className="w-12 h-12 bg-gradient-to-br from-[#2E3A59] to-[#4A99F8] rounded-full flex items-center justify-center text-white shadow-lg hover:shadow-xl transform hover:scale-110 hover:rotate-12 transition-all duration-300 cursor-pointer">
                <BiGlobe size={20} />
              </div>
            </div>

            <div className="space-y-6 text-sm">
              <div className="group">
                <strong className="block text-gray-800 font-bold mb-2 text-base group-hover:text-[#4A99F8] transition-colors duration-300">Passion</strong>
                <p className="text-gray-700 leading-relaxed bg-gradient-to-r from-blue-50 to-purple-50 p-4 rounded-lg border-l-4 border-[#4A99F8] hover:shadow-md transition-all duration-300">{profileData.passion}</p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="group p-4 rounded-lg hover:bg-gradient-to-r hover:from-blue-50 hover:to-purple-50 transition-all duration-300 hover:shadow-md">
                  <strong className="block text-gray-800 font-bold mb-2 group-hover:text-[#4A99F8] transition-colors duration-300">Gender</strong>
                  <p className="text-gray-700 font-medium">{profileData.gender}</p>
                </div>
                <div className="group p-4 rounded-lg hover:bg-gradient-to-r hover:from-blue-50 hover:to-purple-50 transition-all duration-300 hover:shadow-md">
                  <strong className="block text-gray-800 font-bold mb-2 group-hover:text-[#4A99F8] transition-colors duration-300">Date of Birth</strong>
                  <p className="text-gray-700 font-medium">{profileData.dob}</p>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="group p-4 rounded-lg hover:bg-gradient-to-r hover:from-blue-50 hover:to-purple-50 transition-all duration-300 hover:shadow-md">
                  <strong className="block text-gray-800 font-bold mb-2 group-hover:text-[#4A99F8] transition-colors duration-300">Mobile Number</strong>
                  <p className="text-gray-700 font-medium">{profileData.mobile}</p>
                </div>
                <div className="group p-4 rounded-lg hover:bg-gradient-to-r hover:from-blue-50 hover:to-purple-50 transition-all duration-300 hover:shadow-md">
                  <strong className="block text-gray-800 font-bold mb-2 group-hover:text-[#4A99F8] transition-colors duration-300">Occupation</strong>
                  <p className="text-gray-700 font-medium">{profileData.occupation}</p>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="group p-4 rounded-lg hover:bg-gradient-to-r hover:from-blue-50 hover:to-purple-50 transition-all duration-300 hover:shadow-md">
                  <strong className="block text-gray-800 font-bold mb-2 group-hover:text-[#4A99F8] transition-colors duration-300">Email Id</strong>
                  <p className="text-gray-700 font-medium">{profileData.email}</p>
                </div>
                <div className="group p-4 rounded-lg hover:bg-gradient-to-r hover:from-blue-50 hover:to-purple-50 transition-all duration-300 hover:shadow-md">
                  <strong className="block text-gray-800 font-bold mb-2 group-hover:text-[#4A99F8] transition-colors duration-300">Location</strong>
                  <p className="text-gray-700 font-medium">{profileData.location}</p>
                </div>
              </div>

              <div className="group p-4 rounded-lg hover:bg-gradient-to-r hover:from-blue-50 hover:to-purple-50 transition-all duration-300 hover:shadow-md">
                <strong className="block text-gray-800 font-bold mb-2 group-hover:text-[#4A99F8] transition-colors duration-300">Language</strong>
                <p className="text-gray-700 font-medium">{profileData.language}</p>
              </div>
            </div>
          </div>
        </div>

        {/* Ads Section */}
        <div className="w-full lg:w-80 space-y-6">
          {/* First Ad */}
          <div className="bg-white rounded-lg shadow-sm border border-blue-100 p-4">
            <div className="text-center text-blue-600 font-medium mb-3 text-sm">
              Take your website to the next level
            </div>
            <div className="bg-blue-50 p-4 rounded-lg flex justify-center mb-3">
              <img
                src="https://via.placeholder.com/240x120/E3F2FD/1976D2?text=Website+Analytics"
                alt="Website Analytics"
                className="rounded w-full h-auto max-h-24 object-cover"
              />
            </div>
            <button className="w-full bg-blue-600 hover:bg-blue-700 text-white text-xs px-4 py-2 rounded-full font-medium transition-colors">
              Get started
            </button>
          </div>

          {/* Second Ad */}
          <div className="bg-white rounded-lg shadow-sm border border-blue-100 p-4">
            <div className="text-center text-blue-600 font-medium mb-3 text-sm">
              Take your website to the next level
            </div>
            <div className="bg-blue-50 p-4 rounded-lg flex justify-center mb-3">
              <img
                src="https://via.placeholder.com/240x120/E8F5E8/2E7D32?text=Business+Growth"
                alt="Business Growth"
                className="rounded w-full h-auto max-h-24 object-cover"
              />
            </div>
            <button className="w-full bg-blue-600 hover:bg-blue-700 text-white text-xs px-4 py-2 rounded-full font-medium transition-colors">
              Get started
            </button>
          </div>

          {/* Third Ad */}
          <div className="bg-white rounded-lg shadow-sm border border-blue-100 p-4">
            <div className="text-center text-blue-600 font-medium mb-3 text-sm">
              Take your website to the next level
            </div>
            <div className="bg-blue-50 p-4 rounded-lg flex justify-center mb-3">
              <img
                src="https://via.placeholder.com/240x120/F3E5F5/7B1FA2?text=Marketing+Tools"
                alt="Marketing Tools"
                className="rounded w-full h-auto max-h-24 object-cover"
              />
            </div>
            <button className="w-full bg-blue-600 hover:bg-blue-700 text-white text-xs px-4 py-2 rounded-full font-medium transition-colors">
              Get started
            </button>
          </div>
        </div>
      </div>

      {/* Social Links */}
      <div className="bg-white/90 backdrop-blur-sm rounded-2xl border border-gray-200 p-8 mt-8 mx-8 shadow-2xl hover:shadow-3xl transition-all duration-500 relative overflow-hidden group">
        {/* Background Pattern */}
        <div className="absolute inset-0 bg-gradient-to-br from-blue-50/30 via-transparent to-purple-50/30 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

        <div className="relative z-10">
          <h3 className="text-xl font-bold text-gray-800 mb-6 text-center">Connect With Me</h3>
          <div className="space-y-4">
            {[
              {
                icon: <FaInstagram size={20} />,
                color: 'bg-gradient-to-br from-purple-500 to-pink-500',
                label: profileData.social.instagram,
                hoverColor: 'hover:from-purple-600 hover:to-pink-600'
              },
              {
                icon: <FaFacebook size={20} />,
                color: 'bg-blue-600',
                label: profileData.social.facebook,
                hoverColor: 'hover:bg-blue-700'
              },
              {
                icon: <FaLinkedin size={20} />,
                color: 'bg-blue-700',
                label: profileData.social.linkedin,
                hoverColor: 'hover:bg-blue-800'
              },
              {
                icon: <FaTwitter size={20} />,
                color: 'bg-blue-400',
                label: profileData.social.twitter,
                hoverColor: 'hover:bg-blue-500'
              },
              {
                icon: <SiGoodreads size={20} />,
                color: 'bg-amber-700',
                label: profileData.social.goodreads,
                hoverColor: 'hover:bg-amber-800'
              },
            ].map(({ icon, color, label, hoverColor }, idx) => (
              <div key={idx} className="group/item flex items-center gap-5 p-4 hover:bg-gradient-to-r hover:from-blue-50 hover:to-purple-50 rounded-xl transition-all duration-300 cursor-pointer hover:shadow-lg transform hover:scale-105">
                <div className={`w-14 h-14 ${color} ${hoverColor} rounded-full flex items-center justify-center text-white shadow-lg group-hover/item:shadow-xl transition-all duration-300 group-hover/item:scale-110 group-hover/item:rotate-12`}>
                  {icon}
                </div>
                <span className="text-gray-700 text-base font-semibold group-hover/item:text-[#4A99F8] transition-colors duration-300">{label}</span>
                <div className="ml-auto opacity-0 group-hover/item:opacity-100 transition-opacity duration-300">
                  <svg className="w-5 h-5 text-[#4A99F8]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                  </svg>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}

export default MyProfilePage;
