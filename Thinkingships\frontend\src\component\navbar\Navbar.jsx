import { SquarePen } from "lucide-react";
import { useNavigate } from "react-router-dom";

function Navbar() {
    const navigate = useNavigate()
    return (
        <div className="navbar fixed top-0 left-0 right-0 z-50 bg-white shadow-md px-6 py-3 border-b border-gray-200 pl-48">

            {/* Left Nav Links */}
            <ul className="flex gap-6 text-sm font-semibold text-gray-500 ">
                <li><a className="hover:text-[#4A99F8] transition-all duration-200 cursor-pointer hover:scale-105" onClick={()=>navigate("/romance")}>Romance</a></li>
                <li><a className="hover:text-[#4A99F8] transition-all duration-200 cursor-pointer hover:scale-105" onClick={()=>navigate("/fantasy")}>Fantasy</a></li>
                <li><a className="hover:text-[#4A99F8] transition-all duration-200 cursor-pointer hover:scale-105" onClick={()=>navigate("/mystery")}>Mystery</a></li>
                <li><a className="hover:text-[#4A99F8] transition-all duration-200 cursor-pointer hover:scale-105" onClick={()=>navigate("/all")}>Browse All</a></li>
            </ul>

            {/* Center Logo / Branding */}
            <div className="flex-1 flex justify-center items-center gap-2 text-[#4A99F8] " >
                <SquarePen width={26} height={26} onClick={()=>navigate("/skrivee")} className="cursor-pointer transition-all duration-200 hover:scale-105"/>
                <span className="font-bold text-lg tracking-wide cursor-pointer transition-all duration-200 hover:scale-105" onClick={()=>navigate("/skrivee")}>Skrivee</span>
            </div>
        </div>
    );
}

export default Navbar;
