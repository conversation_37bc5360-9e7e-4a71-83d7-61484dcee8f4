import { SquarePen } from "lucide-react";
import { useNavigate } from "react-router-dom";

function Navbar() {
    const navigate = useNavigate()
    return (
        <div className="navbar fixed top-0 left-0 right-0 z-50 bg-white shadow-lg px-6 border-b border-gray-200 pl-48">
            {/* Top Section - Center Logo / Branding */}
            <div className="flex justify-center items-center py-2 border-b border-gray-100">
                <div className="flex items-center gap-2 text-[#4A99F8]">
                    <SquarePen
                        width={28}
                        height={28}
                        onClick={()=>navigate("/skrivee")}
                        className="cursor-pointer transition-all duration-300 hover:scale-110 hover:rotate-12 hover:text-[#3B82F6] drop-shadow-md"
                    />
                    <span
                        className="font-bold text-xl tracking-wide cursor-pointer transition-all duration-300 hover:scale-105 hover:text-[#3B82F6] drop-shadow-sm"
                        onClick={()=>navigate("/skrivee")}
                    >
                        Skrivee
                    </span>
                </div>
            </div>

            {/* Bottom Section - Nav Links */}
            <div className="flex justify-start items-center py-3">
                <ul className="flex gap-8 text-sm font-semibold text-gray-600">
                    <li>
                        <a
                            className="hover:text-[#4A99F8] transition-all duration-300 cursor-pointer hover:scale-105 relative group px-2 py-1 rounded-md hover:bg-blue-50"
                            onClick={()=>navigate("/romance")}
                        >
                            Romance
                            <span className="absolute bottom-0 left-0 w-0 h-0.5 bg-[#4A99F8] transition-all duration-300 group-hover:w-full"></span>
                        </a>
                    </li>
                    <li>
                        <a
                            className="hover:text-[#4A99F8] transition-all duration-300 cursor-pointer hover:scale-105 relative group px-2 py-1 rounded-md hover:bg-blue-50"
                            onClick={()=>navigate("/fantasy")}
                        >
                            Fantasy
                            <span className="absolute bottom-0 left-0 w-0 h-0.5 bg-[#4A99F8] transition-all duration-300 group-hover:w-full"></span>
                        </a>
                    </li>
                    <li>
                        <a
                            className="hover:text-[#4A99F8] transition-all duration-300 cursor-pointer hover:scale-105 relative group px-2 py-1 rounded-md hover:bg-blue-50"
                            onClick={()=>navigate("/mystery")}
                        >
                            Mystery
                            <span className="absolute bottom-0 left-0 w-0 h-0.5 bg-[#4A99F8] transition-all duration-300 group-hover:w-full"></span>
                        </a>
                    </li>
                    <li>
                        <a
                            className="hover:text-[#4A99F8] transition-all duration-300 cursor-pointer hover:scale-105 relative group px-2 py-1 rounded-md hover:bg-blue-50"
                            onClick={()=>navigate("/all")}
                        >
                            Browse All
                            <span className="absolute bottom-0 left-0 w-0 h-0.5 bg-[#4A99F8] transition-all duration-300 group-hover:w-full"></span>
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    );
}

export default Navbar;
