import { BiGlobe } from 'react-icons/bi';
import { FaEnvelope, FaFacebook, FaInstagram, FaLinkedin, FaTwitter, FaUserPlus } from 'react-icons/fa';
import { SiGoodreads } from 'react-icons/si';

function MemberProfile({ user = null }) {
  const profileData = {
    username: 'LitByNandini',
    fullName: '<PERSON><PERSON><PERSON>',
    rank: 1800,
    fans: 150,
    faves: 640,
    bio: 'I am a passionate writer with a keen eye for detail, dedicated to exploring the complexities of the human experience through captivating storytelling that leaves a lasting impression.',
    passion: 'Lorem ipsum dolor sit amet consectetur. Enim rhoncus blandit consequat vel nulla at feugiat volutpat.',
    gender: 'Female',
    dob: '07-03-1991',
    mobile: '+012345678',
    occupation: 'Student',
    email: '<EMAIL>',
    location: 'New Delhi',
    language: 'English',
    social: {
      instagram: 'nandini.kumar',
      facebook: 'nandini.kumar',
      linkedin: 'nandini.kumar',
      twitter: 'nandini.kumar',
      goodreads: 'nandini.kumar',
    }
  };

  return (
    <div className="flex-1 w-full bg-white">
      {/* Profile Header */}
      <div className="bg-white p-6 border-b border-gray-200 text-center">
        <div className="flex justify-center gap-16 items-center mb-4">
          {/* Fans */}
          <div>
            <div className="text-3xl font-semibold">{profileData.fans}</div>
            <div className="text-sm text-gray-600 font-medium">FANS</div>
          </div>

          {/* Circular Profile Image */}
          <div className="w-28 h-28 rounded-full overflow-hidden border-4 border-white shadow-lg">
            <img
              src={user?.profileImage || "https://img.daisyui.com/images/profile/demo/<EMAIL>"}
              alt={profileData.fullName}
              className="w-full h-full object-cover"
            />
          </div>

          {/* Faves */}
          <div>
            <div className="text-3xl font-semibold">{profileData.faves}</div>
            <div className="text-sm text-gray-600 font-medium">FAVES</div>
          </div>
        </div>

        {/* Username + Rank */}
        <div className="text-sm text-gray-500 mb-1">Rank {profileData.rank}</div>
        <h2 className="text-2xl font-semibold mb-4">{profileData.username}</h2>

        {/* Buttons */}
        <div className="flex justify-center gap-4 mt-4 mb-6">
          <button className="flex items-center gap-2 bg-red-600 hover:bg-red-700 text-white px-6 py-2 rounded-full text-sm font-medium">
            <FaUserPlus size={14} />
            <span>Follow</span>
          </button>
          <button className="flex items-center gap-2 bg-gray-800 hover:bg-gray-900 text-white px-6 py-2 rounded-full text-sm font-medium">
            <FaEnvelope size={14} />
            <span>Message</span>
          </button>
        </div>

        {/* Bio */}
        <p className="text-gray-600 text-sm max-w-2xl mx-auto mb-6 leading-relaxed">{profileData.bio}</p>

        {/* Tabs */}
        <div className="flex justify-center gap-4 max-w-md mx-auto">
          <button className="bg-[#2E3A59] text-white px-8 py-2 rounded-md flex-1 text-sm font-medium">INFO</button>
          <button className="border border-gray-300 px-8 py-2 rounded-md flex-1 text-sm font-medium hover:bg-gray-100">SKRIVEE</button>
          <button className="border border-gray-300 px-8 py-2 rounded-md flex-1 text-sm font-medium hover:bg-gray-100">FEEDBACK</button>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex justify-center p-6">
        {/* Info Card */}
        <div className="bg-white rounded-lg border border-gray-300 p-6 w-full max-w-2xl">
          <div className="flex items-center justify-between mb-6">
            <h3 className="font-semibold text-lg text-gray-800">{profileData.fullName}</h3>
            <div className="w-8 h-8 bg-[#2E3A59] rounded-full flex items-center justify-center text-white">
              <BiGlobe size={18} />
            </div>
          </div>

          <div className="space-y-5 text-sm">
            <div>
              <strong className="block text-gray-800 font-semibold mb-1">Passion</strong>
              <p className="text-gray-600 leading-relaxed">{profileData.passion}</p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <strong className="block text-gray-800 font-semibold mb-1">Gender</strong>
                <p className="text-gray-600">{profileData.gender}</p>
              </div>
              <div>
                <strong className="block text-gray-800 font-semibold mb-1">Date of Birth</strong>
                <p className="text-gray-600">{profileData.dob}</p>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <strong className="block text-gray-800 font-semibold mb-1">Mobile Number</strong>
                <p className="text-gray-600">{profileData.mobile}</p>
              </div>
              <div>
                <strong className="block text-gray-800 font-semibold mb-1">Occupation</strong>
                <p className="text-gray-600">{profileData.occupation}</p>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <strong className="block text-gray-800 font-semibold mb-1">Email Id</strong>
                <p className="text-gray-600">{profileData.email}</p>
              </div>
              <div>
                <strong className="block text-gray-800 font-semibold mb-1">Location</strong>
                <p className="text-gray-600">{profileData.location}</p>
              </div>
            </div>

            <div>
              <strong className="block text-gray-800 font-semibold mb-1">Language</strong>
              <p className="text-gray-600">{profileData.language}</p>
            </div>
          </div>
        </div>
      </div>

      {/* Social Links */}
      <div className="bg-white rounded-lg border border-gray-300 p-6 mt-6 mx-6">
        <div className="space-y-4">
          {[
            {
              icon: <FaInstagram size={18} />,
              color: 'bg-gradient-to-br from-purple-500 to-pink-500',
              label: profileData.social.instagram
            },
            {
              icon: <FaFacebook size={18} />,
              color: 'bg-blue-600',
              label: profileData.social.facebook
            },
            {
              icon: <FaLinkedin size={18} />,
              color: 'bg-blue-700',
              label: profileData.social.linkedin
            },
            {
              icon: <FaTwitter size={18} />,
              color: 'bg-blue-400',
              label: profileData.social.twitter
            },
            {
              icon: <SiGoodreads size={18} />,
              color: 'bg-amber-700',
              label: profileData.social.goodreads
            },
          ].map(({ icon, color, label }, idx) => (
            <div key={idx} className="flex items-center gap-4 p-2 hover:bg-gray-50 rounded-lg transition-colors cursor-pointer">
              <div className={`w-12 h-12 ${color} rounded-full flex items-center justify-center text-white shadow-sm`}>
                {icon}
              </div>
              <span className="text-gray-700 text-sm font-medium">{label}</span>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}

export default MemberProfile;
